#!/bin/bash
rm -f /var/opt/gitlab/backups/*_$(date -d "10 day ago"  +"%Y_%m_%d")_11.0.1_gitlab_backup.tar
gitlab-rake gitlab:backup:create
sleep 10
#SFTP配置信息
#IP
IP=************
#端口
PORT=22
#用户名
USER=admin
#密码
PASSWORD=synctech
#待上传文件根目录
CLIENTDIR=/var/opt/gitlab/backups
#SFTP目录
SEVERDIR=/share/homes/git_bak
#待上传文件名
cd ${CLIENTDIR}
FILE=$(ls *_$(date +"%Y_%m_%d")_11.0.1_gitlab_backup.tar | awk '{print $NF}')
echo ${FILE}

lftp -u ${USER},${PASSWORD} sftp://${IP}:${PORT} <<EOF
cd ${SEVERDIR}/
lcd ${CLIENTDIR}
put ${FILE}
by
EOF
echo "commit to upload git_bak_file successfully"
